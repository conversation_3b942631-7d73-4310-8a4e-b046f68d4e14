<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import { enhance } from '$app/forms';
    import {
        Button,
        Label,
        Input,
        Alert,
        Modal,
        Checkbox,
    } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    import { fly } from 'svelte/transition';
    import { CheckCircleSolid } from 'flowbite-svelte-icons';
    import { toastStore } from '$lib/stores/toastStore';
  
    export let user: any;
  
    // messages + loading
    let isSubmitting = false;
    let isPasswordSubmitting = false;
  
    // form data
    let formData = {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
        employee_id: user.employee_id,
        first_name: user.first_name,
        last_name: user.last_name,
        department: user.department || '',
        role: user.role,
        is_active: user.is_active,
        phone_number: user.phone_number || '',
    };
  
    // Password modal state
    let passwordModalOpen = false;
    let passwordFormData = {
        old_password: '',
        new_password: '',
        confirm_password: ''
    };
    let passwordFieldsEverTyped = false;

    // Shared enhance options
    $: enhanceOptions = {
        modalOpen: passwordModalOpen,
        setModalOpen: (value: boolean) => passwordModalOpen = value,
        setPending: (value: boolean, isPassword: boolean = false) => 
            isPassword ? (isPasswordSubmitting = value) : (isSubmitting = value)
    };

    // Password rules
    const specialChars = '!@#$%^&*';
    function checkPasswordRules(password: string) {
        return {
            length: password.length > 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(password),
            number: /[0-9]/.test(password)
        };
    }
    
    $: passwordRulesStatus = checkPasswordRules(passwordFormData.new_password);
    
    $: allPasswordRulesPassed = Object.values(passwordRulesStatus).every(value => value === true);
    $: passwordsMatch = passwordFormData.new_password === passwordFormData.confirm_password && passwordFormData.new_password.length > 0;

    // Handle form submission results
    const handleSubmissionResult = (result: any, isPassword: boolean = false) => {
        if (result.type === 'failure') {
            toastStore.add(result.data?.res_msg || 'Operation failed', 'error');
        } else if (result.type === 'success') {
            toastStore.add(result.data?.res_msg || (isPassword ? 'Password changed successfully' : 'Profile updated successfully'), 'success');
            if (isPassword) {
                enhanceOptions.setModalOpen(false);
            }
        }
    };

    // Enhance handlers
    const handleProfileEnhance = () => {
        return async ({ result, update }) => {
            enhanceOptions.setPending(true);
            handleSubmissionResult(result);
            await update();
            enhanceOptions.setPending(false);
        };
    };

    const handlePasswordEnhance = () => {
        return async ({ result, update }) => {
            enhanceOptions.setPending(true, true);
            handleSubmissionResult(result, true);
            await update();
            enhanceOptions.setPending(false, true);
        };
    };

    // Watch modal state to clean up everything
    $: if (passwordModalOpen === false) {
        passwordFormData = {
            old_password: '',
            new_password: '',
            confirm_password: ''
        };
        passwordFieldsEverTyped = false;
    }
</script>


<!-- {#if showSuccess}
    <Alert color="success" on:dismiss={() => showSuccess = false}>
        <span class="font-medium">{successMessage}</span>
    </Alert>
{/if} 

{#if showError}
    <Alert color="failure" on:dismiss={() => showError = false}>
        <span class="font-medium">{errorMessage}</span>
    </Alert>
{/if} -->
  
<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <form
        action="?/update_user"
        method="POST"
        use:enhance={handleProfileEnhance}
        class="space-y-4"
    >

        <div class="flex items-center justify-between w-full">
            <div>
                <h2 class="text-xl font-medium text-gray-700">{t('your_info_title')}</h2>
                <p class="text-sm text-gray-500">{t('your_info_description')}</p>
            </div>
        
            <Button
                type="submit"
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors"
                disabled={isSubmitting}
            >
                {#if isSubmitting}
                    {t('saving')}
                {:else}
                    {t('update')}
                {/if}
            </Button>
        </div>
      
        <div>
            <Label for="name" class="text-left">{t('name')}</Label>
            <Input
            id="name"
            name="name"
            type="text"
            bind:value={formData.name}
            required
            />
        </div>
    
        <div class="grid grid-cols-2 gap-4">
            <div>
                <Label for="first_name" class="text-left">{t('first_name')}</Label>
                <Input
                    id="first_name"
                    name="first_name"
                    type="text"
                    bind:value={formData.first_name}
                    required
                />
            </div>

            <div>
                <Label for="last_name" class="text-left">{t('last_name')}</Label>
                <Input
                    id="last_name"
                    name="last_name"
                    type="text"
                    bind:value={formData.last_name}
                    required
                />
            </div>
        </div>
  
        <div class="grid grid-cols-2 gap-4">
            <div>
                <Label for="email" class="text-left">{t('email')}</Label>
                <Input
                id="email"
                name="email"
                type="email"
                bind:value={formData.email}
                required
                />
            </div>

            <div>
                <Label for="phone_number" class="text-left">{t('phone_number')}</Label>
                <Input
                    id="phone_number"
                    name="phone_number"
                    type="text"
                    bind:value={formData.phone_number}
                    required
                />
            </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
            <div>
                <Label for="password" class="text-left text-black focus:text-blue">{t('password')}</Label>
                <div class="flex gap-2">
                    <Input
                        id="password"
                        name="password"
                        type="password"
                        value="••••••••••••••••••"
                        disabled
                        class="bg-gray-100 flex-1"
                    />
                    <Button
                        type="button"
                        class="bg-white border border-gray-300 hover:bg-gray-500 hover:text-white text-black font-medium rounded-lg transition-colors whitespace-nowrap"
                        on:click={() => passwordModalOpen = true}
                    >
                        {t('change_password')}
                    </Button>
                </div>
            </div>
        </div>
    
        <input type="hidden" name="is_active" value={formData.is_active}>
        <input type="hidden" name="id" value={formData.id}>
        <input type="hidden" name="username" value={formData.username}>
    </form>
</div>

<Modal bind:open={passwordModalOpen} size="xs" outsideclose>
    <div class="text-2xl font-semibold mb-4 text-black">{t('change_password')}</div>
    <div class="p-2">
        <form
            action="?/change_password"
            method="POST"
            use:enhance={handlePasswordEnhance}
            class="space-y-3"
        >
            <div class="grid gap-2">
                <Label for="old_password" class="space-y-2 text-left text-sm">{t('current_password')}</Label>
                <Input 
                    id="old_password"
                    name="old_password"
                    type="password" 
                    bind:value={passwordFormData.old_password}
                    placeholder="{t('current_password_placeholder')}" 
                    required 
                />
            </div>
            <div class="grid gap-2">
                <Label for="new_password" class="space-y-2 text-left text-sm">{t('new_password')}</Label>
                <Input 
                    id="new_password"
                    name="new_password"
                    type="password" 
                    bind:value={passwordFormData.new_password}
                    placeholder={t('new_password_placeholder')} 
                    required 
                    on:input={() => passwordFieldsEverTyped = true}
                />
                <div>
                    <div class="text-xs font-normal mb-1 text-gray-400">{t('password_validation_msg_1')}</div>
                    <ul class="text-xs space-y-0">
                        <li class="flex items-center">
                            <span class={
                                passwordRulesStatus.length
                                    ? 'text-gray-400'
                                    : passwordFieldsEverTyped ? 'text-red-600' : 'text-gray-400'
                            }>{t('password_validation_msg_2')}</span>
                        </li>
                        <li class="flex items-center">
                            <span class={
                                passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
                                    ? 'text-gray-400'
                                    : passwordFieldsEverTyped ? 'text-red-600' : 'text-gray-400'
                            }>{t('password_validation_msg_3')}</span>
                        </li>
                        <li class="flex items-center">
                            <span class={
                                passwordRulesStatus.number
                                    ? 'text-gray-400'
                                    : passwordFieldsEverTyped ? 'text-red-600' : 'text-gray-400'
                            }>{t('password_validation_msg_4')}</span>
                        </li>
                        <li class="flex items-center">
                            <span class={
                                passwordRulesStatus.special
                                    ? 'text-gray-400'
                                    : passwordFieldsEverTyped ? 'text-red-600' : 'text-gray-400'
                            }>{t('password_validation_msg_5')} ({specialChars})</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="grid gap-2">
                <Label for="confirm_password" class="space-y-2 text-left text-sm">{t('confirm_password')}</Label>
                <Input 
                    id="confirm_password"
                    name="confirm_password"
                    type="password" 
                    bind:value={passwordFormData.confirm_password}
                    placeholder={t('confirm_new_password_placeholder')} 
                    required 
                    on:input={() => passwordFieldsEverTyped = true}
                />
                <div style="min-height:1em;" class="flex justify-left items-top">
                    {#if passwordFieldsEverTyped && !passwordsMatch && passwordFormData.confirm_password.length > 0}
                        <span class="text-xs text-red-600 text-left">  Passwords do not match.</span>
                    {/if}
                </div>
            </div>
            <div class="flex gap-2 mt-6 items-center">
                <Button type="submit" color="blue" disabled={!allPasswordRulesPassed || !passwordsMatch}
                    on:click={() => {
                        // console.log('Password Submission Debug:', {
                        //     errorMessage,
                        //     passwordRules: passwordRulesStatus,
                        //     allRulesPassed: allPasswordRulesPassed,
                        //     passwordsMatch,
                        //     formData: {
                        //         oldPasswordLength: passwordFormData.old_password.length,
                        //         newPasswordLength: passwordFormData.new_password.length,
                        //         confirmPasswordLength: passwordFormData.confirm_password.length
                        //     }
                        // });
                    }}>
                    {t('submit')}
                </Button>
                <Button color="alternative"
                    on:click={() => {
                        passwordModalOpen = false;
                    }}
                >
                    {t('cancel')}
                </Button>
            </div>
        </form>
    </div>
</Modal>